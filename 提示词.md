优化页面：
1. 设定值单元格中，toggle-switch 靠左排列、switch-status-text 靠右排列
2. 控制模式1中的参数名称普遍短，列的长度可以短些，这样界面好看些





参考 debug1/保护使能.html ，完成 “debug2/控制模式.html”


参数列表如下：

组	序号	参数名称	标识符	模型类别	数据类型	页面元素形态	备注
控制模式1	1	手动给定无功	1914_RPC	属性	integer	开关	1 - 手动；0 - 自动
	2	控制模式选择	1914_CM	属性	integer	开关	1 - 直接；0 - 间接
	3	单元级数	1914_UL	属性	integer	整数输入框	取值范围 10~12
	4	相数	1914_PN	属性	integer	开关	1 - 三相；0 - 单相
	5	SVG 连接方式	1914_SCM	属性	integer	开关	1 - 角接；0 - 星接
	6	平衡控制使能	1914_BCE	属性	integer	开关	1 - 有；0 - 无
	7	电压前馈模式	1914_GVFF	属性	integer	开关	1 - 加滤波；0 - 无滤波
	8	空载模式使能	1914_NLME	属性	integer	开关	1 - 使能；0 - 不使能
	9	锁相环模式	1914_PM	属性	integer	开关	1-SOGI 模式；0 - 正常模式
	10	低电压无功支撑	1914_LVRSE	属性	integer	开关	1 - 使能；0 - 不使能
	11	CT 采样点位置	1914_CSM	属性	integer	开关	1 - 负载侧；0 - 电网侧
	12	主从机模式	1914_MSM	属性	integer	开关	1 - 主机；0 - 从机
	13	空载模式	1914_NLM	属性	integer	开关	1 - 发容性；0 - 发感性
	14	母联状态	1914_BTM	属性	integer	开关	1 - 考虑；0 - 不考虑
	15	无功补偿目标点	1914_RCT	属性	integer	开关	1 - 高压侧；0 - 低压侧
	16	低电压穿越使能	1914_LVRT	属性	integer	开关	1 - 低电压；0 - 正常补偿模式
	17	快速无功补偿使能	1914_FRCE	属性	integer	开关	1 - 使能；0 - 不使能
	18	测试模式	1914_TM	属性	integer	开关	1 - 方波测试模式；0 - 正常模式
	19	柜体排列方式	1914_CA	属性	integer	开关	1 - 左排列；0 - 右排列
	20	谐波补偿类型	1914_HCAM	属性	integer	开关	1 - 手动运行；0 - 自动运行
	21	平衡控制电压指令	1914_BCVC	属性	integer	开关	1-CPU 板 Ref；0-PWM 板 Ref
	22	运行模式	1914_OM	属性	integer	下拉选择框	0-动态补偿模式；1-功率因数模式；2-电压控制模式；3-无功电压模式
	23	AVC 无功设定模式	1914_ARSM	属性	integer	开关	1 - 使能；0 - 不使能
	24	电压补偿目标点	1914_VCT	属性	integer	开关	1 - 高压侧；0 - 低压侧
	25	直流电流抑制模式	1914_DCSM	属性	integer	下拉选择框	0 - 不使能；1 - 使能；2 - 使能；
	26	负序电流抑制模式	1914_NSCS	属性	integer	开关	1 - 使能；0 - 不使能
控制模式2	1	无功补偿并联	1960_RPOE	属性	integer	开关	1 - 使能；0 - 不使能
	2	电压补偿并联	1960_VPOE	属性	integer	开关	1 - 使能；0 - 不使能
	3	并联运行电压目标值同步	1960_TVOVS	属性	integer	开关	1 - 同步；0 - 不同步
	4	备用	1960_RSV	属性	integer	开关	1-；-
控制模式3	1	电压补偿点	1961_VCP	属性	integer	下拉选择框	0-基波正序线电压；1-Uab 线电压；2-Ubc 线电压；3-Uca 线电压
	2	负载无功快速检测	1961_LRFDM	属性	integer	开关	1 - 负载电流检测；0-PQ 模式
