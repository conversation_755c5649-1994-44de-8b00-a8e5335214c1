<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制模式 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 控制模式专用样式 -->
    <style>
        /* 控制模式专用样式扩展 */
        .control-mode-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px; /* 减少间距 */
            position: relative;
            padding-bottom: 70px; /* 减少底部预留空间 */
            overflow: hidden; /* 防止内容溢出 */
            min-height: 0; /* 允许收缩 */
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 整数输入框样式 */
        .integer-input {
            width: 80px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .integer-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .integer-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 下拉选择框样式 */
        .select-dropdown {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            font-size: 11px;
            font-weight: bold;
            margin: 0 auto;
            cursor: pointer;
            display: block;
        }

        .select-dropdown:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .select-dropdown option {
            background: rgba(42, 49, 66, 0.95);
            color: #ffffff;
            padding: 5px;
        }

        /* 开关控件容器样式 - 水平布局，开关在左，文本在右 */
        .switch-control-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 100%;
            padding: 4px;
            gap: 8px; /* 开关和文本之间的间距 */
        }

        /* 重写开关样式以确保一致性 */
        .toggle-switch {
            position: relative;
            width: 60px !important;
            height: 30px !important;
            background: #dc3545;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
            flex-shrink: 0; /* 防止收缩 */
            margin: 0; /* 移除margin，使用容器的gap控制间距 */
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(30px);
        }

        /* 开关状态文本样式 - 放在开关右侧 */
        .switch-status-text {
            font-size: 11px;
            color: #7a8ba0;
            text-align: left;
            width: 60px; /* 固定宽度，防止影响开关位置 */
            height: 30px; /* 与开关同高 */
            line-height: 30px; /* 垂直居中 */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0;
            flex-shrink: 0; /* 防止收缩 */
        }

        .switch-status-text.active {
            color: #00d4ff;
        }

        /* 调整参数设置列宽度以适应不同控件 */
        .param-setting {
            width: 130px; /* 增加宽度以容纳开关和状态文本 */
            text-align: center;
            vertical-align: middle;
            padding: 3px;
        }

        /* 新的布局样式：左侧大面板，右侧上下两个小面板 */
        .main-layout {
            display: flex;
            gap: 12px; /* 减少间距 */
            height: calc(100vh - 120px); /* 进一步优化高度计算 */
            max-height: calc(100vh - 120px);
        }

        .left-panel {
            flex: 2;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px; /* 减少间距 */
            min-height: 0;
            overflow: hidden;
        }

        .control-mode-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px; /* 减少内边距 */
            overflow: hidden;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        /* 控制模式1占用左侧全部空间 */
        .control-mode-panel.mode1 {
            flex: 1;
            min-height: 0;
        }

        /* 控制模式2和3平分右侧空间 */
        .control-mode-panel.mode2,
        .control-mode-panel.mode3 {
            flex: 1;
            min-height: 0;
        }

        /* 表格容器样式 */
        .table-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px; /* 减少底部边距 */
            padding: 6px 0; /* 减少内边距 */
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0; /* 防止收缩 */
        }

        /* 表格样式优化 - 支持每行两个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 6px 3px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 12px;
        }

        .params-table td {
            padding: 3px 2px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 50px; /* 优化行高 */
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            font-weight: bold;
            width: 30px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 12px;
            color: #ffffff;
            text-align: left;
            padding-left: 4px;
            line-height: 1.2;
            max-width: 130px;
            width: 130px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
            width: 70px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 130px;
            text-align: center;
            padding: 0 5px;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>控制模式</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 左侧面板：控制模式1 -->
                <div class="left-panel">
                    <div class="control-mode-panel mode1">
                        <div class="panel-title">控制模式1</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-table-1">
                                    <!-- 控制模式1参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 右侧面板：控制模式2和3 -->
                <div class="right-panel">
                    <!-- 控制模式2面板 -->
                    <div class="control-mode-panel mode2">
                        <div class="panel-title">控制模式2</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-table-2">
                                    <!-- 控制模式2参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 控制模式3面板 -->
                    <div class="control-mode-panel mode3">
                        <div class="panel-title">控制模式3</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-table-3">
                                    <!-- 控制模式3参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="sendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        /**
         * 控制模式页面配置
         * 定义控制模式参数列表和页面设置
         */

        // 控制模式1组 (26个参数)
        const controlMode1Group = [
            { mqttId: '1914_RPC', name: '手动给定无功', type: 'switch', options: ['自动', '手动'] },
            { mqttId: '1914_CM', name: '控制模式选择', type: 'switch', options: ['间接', '直接'] },
            { mqttId: '1914_UL', name: '单元级数', type: 'integer', min: 10, max: 12 },
            { mqttId: '1914_PN', name: '相数', type: 'switch', options: ['单相', '三相'] },
            { mqttId: '1914_SCM', name: 'SVG连接方式', type: 'switch', options: ['星接', '角接'] },
            { mqttId: '1914_BCE', name: '平衡控制使能', type: 'switch', options: ['无', '有'] },
            { mqttId: '1914_GVFF', name: '电压前馈模式', type: 'switch', options: ['无滤波', '加滤波'] },
            { mqttId: '1914_NLME', name: '空载模式使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '1914_PM', name: '锁相环模式', type: 'switch', options: ['正常模式', 'SOGI模式'] },
            { mqttId: '1914_LVRSE', name: '低电压无功支撑', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '1914_CSM', name: 'CT采样点位置', type: 'switch', options: ['电网侧', '负载侧'] },
            { mqttId: '1914_MSM', name: '主从机模式', type: 'switch', options: ['从机', '主机'] },
            { mqttId: '1914_NLM', name: '空载模式', type: 'switch', options: ['发感性', '发容性'] },
            { mqttId: '1914_BTM', name: '母联状态', type: 'switch', options: ['不考虑', '考虑'] },
            { mqttId: '1914_RCT', name: '无功补偿目标点', type: 'switch', options: ['低压侧', '高压侧'] },
            { mqttId: '1914_LVRT', name: '低电压穿越使能', type: 'switch', options: ['正常补偿模式', '低电压穿越模式'] },
            { mqttId: '1914_FRCE', name: '快速无功补偿使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '1914_TM', name: '测试模式', type: 'switch', options: ['正常模式', '方波测试模式'] },
            { mqttId: '1914_CA', name: '柜体排列方式', type: 'switch', options: ['右排列', '左排列'] },
            { mqttId: '1914_HCAM', name: '谐波补偿类型', type: 'switch', options: ['自动运行', '手动运行'] },
            { mqttId: '1914_BCVC', name: '平衡控制电压指令', type: 'switch', options: ['PWM板Ref', 'CPU板Ref'] },
            { mqttId: '1914_OM', name: '运行模式', type: 'select', options: ['动态补偿模式', '功率因数模式', '电压控制模式', '无功电压模式'] },
            { mqttId: '1914_ARSM', name: 'AVC无功设定模式', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '1914_VCT', name: '电压补偿目标点', type: 'switch', options: ['低压侧', '高压侧'] },
            { mqttId: '1914_DCSM', name: '直流电流抑制模式', type: 'select', options: ['不使能', '使能', '备用'] },
            { mqttId: '1914_NSCS', name: '负序电流抑制模式', type: 'switch', options: ['不使能', '使能'] }
        ];

        // 控制模式2组 (4个参数)
        const controlMode2Group = [
            { mqttId: '1960_RPOE', name: '无功补偿并联', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '1960_VPOE', name: '电压补偿并联', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '1960_TVOVS', name: '并联运行电压目标值同步', type: 'switch', options: ['不同步', '同步'] },
            { mqttId: '1960_RSV', name: '备用', type: 'switch', options: ['-', '-'] }
        ];

        // 控制模式3组 (2个参数)
        const controlMode3Group = [
            { mqttId: '1961_VCP', name: '电压补偿点', type: 'select', options: ['基波正序线电压', 'Uab线电压', 'Ubc线电压', 'Uca线电压'] },
            { mqttId: '1961_LRFDM', name: '负载无功快速检测', type: 'switch', options: ['PQ模式', '负载电流检测'] }
        ];

        // 合并所有参数
        const allControlParameters = [...controlMode1Group, ...controlMode2Group, ...controlMode3Group];

        // 页面配置对象
        const controlModeConfig = {
            pageTitle: '控制模式',
            panelTitles: ['控制模式1', '控制模式2', '控制模式3'],
            parameters: allControlParameters,
            parametersPerPanel: [26, 4, 2], // 每个面板的参数数量
            parametersPerRow: 1 // 每行显示1个参数（单列布局）
        };

        /**
         * 控制模式专用参数配置管理类
         * 扩展通用参数配置管理器以支持不同类型的控件
         */
        class ControlModeParameterManager extends ParameterConfigManager {
            constructor(config) {
                super(config);
            }

            /**
             * 初始化参数定义（重写以确保正确的序号）
             */
            initializeParams() {
                if (!this.config.parameters || !Array.isArray(this.config.parameters)) {
                    console.error('参数配置无效：parameters 必须是数组');
                    return;
                }

                this.config.parameters.forEach((param, index) => {
                    // 计算参数属于哪个面板
                    let panelIndex = 1;
                    let paramIndexInPanel = index + 1;
                    let currentIndex = 0;

                    for (let i = 0; i < this.config.parametersPerPanel.length; i++) {
                        if (currentIndex + this.config.parametersPerPanel[i] > index) {
                            panelIndex = i + 1;
                            paramIndexInPanel = index - currentIndex + 1;
                            break;
                        }
                        currentIndex += this.config.parametersPerPanel[i];
                    }

                    this.parameters.push({
                        id: `param_${index + 1}`,
                        index: paramIndexInPanel, // 在面板内的序号
                        globalIndex: index + 1,   // 全局序号
                        mqttId: param.mqttId,
                        name: param.name,
                        currentValue: 0,
                        settingValue: 0,
                        panel: panelIndex,
                        isInitialized: false
                    });
                });

                console.log(`初始化了 ${this.parameters.length} 个参数`);
            }

            /**
             * 创建参数表格界面（重写以支持每行两个参数的布局）
             */
            createParamTables() {
                const panelCount = this.config.panelTitles.length;
                let paramIndex = 0;

                // 更新页面标题
                if (this.config.pageTitle) {
                    const headerTitle = document.querySelector('.header h1');
                    if (headerTitle) {
                        headerTitle.textContent = this.config.pageTitle;
                    }
                }

                // 为每个面板创建表格
                for (let panelIndex = 1; panelIndex <= panelCount; panelIndex++) {
                    const panelTitle = document.querySelector(`.control-mode-panel.mode${panelIndex} .panel-title`);
                    const tableBody = document.getElementById(`control-table-${panelIndex}`);

                    if (panelTitle && this.config.panelTitles[panelIndex - 1]) {
                        panelTitle.textContent = this.config.panelTitles[panelIndex - 1];
                    }

                    if (tableBody) {
                        // 获取当前面板的参数数量
                        const paramCount = this.config.parametersPerPanel[panelIndex - 1];

                        // 按每行两个参数创建表格行
                        for (let i = 0; i < paramCount; i += 2) {
                            const leftParam = this.parameters[paramIndex + i];
                            const rightParam = this.parameters[paramIndex + i + 1];

                            if (leftParam) {
                                const row = this.createTwoParamRow(leftParam, rightParam);
                                tableBody.appendChild(row);
                            }
                        }

                        paramIndex += paramCount;
                    }
                }

                // 初始化所有参数的高亮状态
                this.initializeAllHighlights();
            }

            /**
             * 创建包含两个参数的表格行
             * @param {Object} leftParam - 左侧参数对象
             * @param {Object} rightParam - 右侧参数对象（可能为null）
             */
            createTwoParamRow(leftParam, rightParam) {
                const row = document.createElement('tr');

                // 创建左侧参数的HTML
                const leftHTML = this.createSingleParamHTML(leftParam);

                // 创建右侧参数的HTML（如果存在）
                let rightHTML = '';
                if (rightParam) {
                    rightHTML = this.createSingleParamHTML(rightParam);
                } else {
                    // 如果没有右侧参数，创建空的占位单元格
                    rightHTML = `
                        <td class="param-index"></td>
                        <td class="param-name"></td>
                        <td class="param-setting"></td>
                        <td class="param-current"></td>
                    `;
                }

                row.innerHTML = leftHTML + rightHTML;
                return row;
            }

            /**
             * 创建单个参数的HTML内容
             * @param {Object} param - 参数对象
             */
            createSingleParamHTML(param) {
                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);

                let controlHTML = '';

                // 根据参数类型创建不同的控件
                switch (paramConfig.type) {
                    case 'switch':
                        controlHTML = this.createSwitchControl(param, paramConfig);
                        break;
                    case 'integer':
                        controlHTML = this.createIntegerControl(param, paramConfig);
                        break;
                    case 'select':
                        controlHTML = this.createSelectControl(param, paramConfig);
                        break;
                    default:
                        controlHTML = this.createSwitchControl(param, paramConfig);
                }

                // 获取当前值的显示文本
                const currentValueText = this.getCurrentValueDisplayText(param, paramConfig);

                return `
                    <td class="param-index" data-param-id="${param.id}" data-cell-type="index">${param.index}</td>
                    <td class="param-name" data-param-id="${param.id}" data-cell-type="name">${param.name}</td>
                    <td class="param-setting" data-param-id="${param.id}" data-cell-type="setting">
                        ${controlHTML}
                    </td>
                    <td class="param-current" data-param-id="${param.id}" data-cell-type="current" id="current-${param.id}">${currentValueText}</td>
                `;
            }

            /**
             * 获取当前值的显示文本
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            getCurrentValueDisplayText(param, config) {
                switch (config.type) {
                    case 'switch':
                        return config.options && config.options[param.currentValue] ? config.options[param.currentValue] : param.currentValue;
                    case 'select':
                        return config.options && config.options[param.currentValue] ? config.options[param.currentValue] : param.currentValue;
                    case 'integer':
                        return param.currentValue;
                    default:
                        return param.currentValue;
                }
            }

            /**
             * 创建开关控件
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            createSwitchControl(param, config) {
                const statusText = config.options ? config.options[param.settingValue] || param.settingValue : param.settingValue;
                return `
                    <div class="switch-control-container">
                        <div class="toggle-switch ${param.settingValue ? 'active' : ''}"
                             id="toggle-${param.id}"
                             onclick="controlModeManager.toggleParam('${param.id}')">
                        </div>
                        <div class="switch-status-text ${param.settingValue ? 'active' : ''}" id="status-text-${param.id}">
                            ${statusText}
                        </div>
                    </div>
                `;
            }

            /**
             * 创建整数输入控件
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            createIntegerControl(param, config) {
                return `
                    <div class="control-container">
                        <input type="number"
                               class="integer-input"
                               id="input-${param.id}"
                               value="${param.settingValue}"
                               min="${config.min || 0}"
                               max="${config.max || 100}"
                               onchange="controlModeManager.updateIntegerParam('${param.id}', this.value)"
                               onblur="controlModeManager.validateIntegerParam('${param.id}', this)">
                    </div>
                `;
            }

            /**
             * 创建下拉选择控件
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            createSelectControl(param, config) {
                let optionsHTML = '';
                if (config.options && Array.isArray(config.options)) {
                    config.options.forEach((option, index) => {
                        const selected = param.settingValue === index ? 'selected' : '';
                        optionsHTML += `<option value="${index}" ${selected}>${option}</option>`;
                    });
                }

                return `
                    <div class="control-container">
                        <select class="select-dropdown"
                                id="select-${param.id}"
                                onchange="controlModeManager.updateSelectParam('${param.id}', this.value)">
                            ${optionsHTML}
                        </select>
                    </div>
                `;
            }

            /**
             * 切换开关参数
             * @param {string} paramId - 参数ID
             */
            toggleParam(paramId) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);
                if (paramConfig.type !== 'switch') return;

                // 切换设定值
                param.settingValue = param.settingValue ? 0 : 1;

                // 更新界面
                const toggle = document.getElementById(`toggle-${paramId}`);
                const statusText = document.getElementById(`status-text-${paramId}`);

                if (param.settingValue) {
                    toggle.classList.add('active');
                    statusText.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                    statusText.classList.remove('active');
                }

                // 更新状态文本
                if (paramConfig.options) {
                    statusText.textContent = paramConfig.options[param.settingValue] || param.settingValue;
                } else {
                    statusText.textContent = param.settingValue;
                }

                // 更新高亮状态
                this.updateHighlightStatus(paramId);

                // 记录修改
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 更新整数参数
             * @param {string} paramId - 参数ID
             * @param {string} value - 新值
             */
            updateIntegerParam(paramId, value) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);
                if (!paramConfig) return;

                const numValue = parseInt(value, 10);
                const min = paramConfig.min || 0;
                const max = paramConfig.max || 100;

                if (isNaN(numValue)) {
                    // 使用默认值，特殊处理单元级数
                    let defaultValue = 11; // 单元级数的默认值
                    if (param.mqttId !== '1914_UL') {
                        defaultValue = Math.max(min, Math.min(max, param.settingValue || min));
                    }
                    
                    param.settingValue = defaultValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = defaultValue;
                    }
                } else if (numValue < min || numValue > max) {
                    // 超出范围，使用默认值
                    let defaultValue = 11; // 单元级数的默认值
                    if (param.mqttId !== '1914_UL') {
                        defaultValue = Math.max(min, Math.min(max, param.settingValue || min));
                    }
                    
                    param.settingValue = defaultValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = defaultValue;
                    }
                    
                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为默认值 ${defaultValue}`, 'warning');
                } else {
                    // 有效值
                    param.settingValue = numValue;
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 验证整数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateIntegerParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);

                if (!param || !paramConfig) return;

                const value = parseInt(inputElement.value, 10);
                const min = paramConfig.min || 0;
                const max = paramConfig.max || 100;

                if (isNaN(value) || value < min || value > max) {
                    // 使用默认值，特殊处理单元级数
                    let defaultValue = 11; // 单元级数的默认值
                    if (param.mqttId !== '1914_UL') {
                        defaultValue = Math.max(min, Math.min(max, param.settingValue || min));
                    }
                    
                    param.settingValue = defaultValue;
                    inputElement.value = defaultValue;
                    
                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为默认值 ${defaultValue}`, 'warning');
                    
                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新下拉选择参数
             * @param {string} paramId - 参数ID
             * @param {string} value - 新值
             */
            updateSelectParam(paramId, value) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const numValue = parseInt(value, 10);
                if (isNaN(numValue)) return;

                param.settingValue = numValue;
                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 重写以支持不同类型控件的界面更新
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                const param = this.parameters.find(p => p.mqttId === mqttId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === mqttId);
                if (!paramConfig) return;

                // 更新参数当前值
                param.currentValue = value;

                // 首次数据同步：如果参数未初始化，将设定值设为当前值
                if (!param.isInitialized) {
                    param.settingValue = value;
                    param.isInitialized = true;

                    // 根据控件类型更新界面
                    this.updateControlInterface(param, paramConfig);

                    // 记录修改
                    this.modifiedValues[param.id] = param.settingValue;

                    console.log(`首次同步参数 ${param.name}: 当前值=${value}, 设定值=${value} (已初始化)`);
                }

                // 更新界面显示（当前值）- 显示对应的文本而不是数值
                const currentElement = document.getElementById(`current-${param.id}`);
                if (currentElement) {
                    const displayText = this.getCurrentValueDisplayText(param, paramConfig);
                    currentElement.textContent = displayText;
                    currentElement.style.color = '#00d4ff';
                }

                // 更新高亮状态
                this.updateHighlightStatus(param.id);
            }

            /**
             * 根据控件类型更新界面
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            updateControlInterface(param, config) {
                switch (config.type) {
                    case 'switch':
                        this.updateSwitchInterface(param, config);
                        break;
                    case 'integer':
                        this.updateIntegerInterface(param, config);
                        break;
                    case 'select':
                        this.updateSelectInterface(param, config);
                        break;
                }
            }

            /**
             * 更新开关控件界面
             */
            updateSwitchInterface(param, config) {
                const toggleElement = document.getElementById(`toggle-${param.id}`);
                const statusTextElement = document.getElementById(`status-text-${param.id}`);

                if (toggleElement) {
                    if (param.settingValue) {
                        toggleElement.classList.add('active');
                    } else {
                        toggleElement.classList.remove('active');
                    }
                }

                if (statusTextElement) {
                    if (param.settingValue) {
                        statusTextElement.classList.add('active');
                    } else {
                        statusTextElement.classList.remove('active');
                    }

                    if (config.options) {
                        statusTextElement.textContent = config.options[param.settingValue] || param.settingValue;
                    } else {
                        statusTextElement.textContent = param.settingValue;
                    }
                }
            }

            /**
             * 更新整数输入控件界面
             */
            updateIntegerInterface(param, config) {
                const inputElement = document.getElementById(`input-${param.id}`);
                if (inputElement) {
                    inputElement.value = param.settingValue;
                }
            }

            /**
             * 更新下拉选择控件界面
             */
            updateSelectInterface(param, config) {
                const selectElement = document.getElementById(`select-${param.id}`);
                if (selectElement) {
                    selectElement.value = param.settingValue;
                }
            }
        }

        // 全局控制模式管理器变量
        let controlModeManager = null;

        /**
         * 初始化控制模式配置页面
         * @param {Object} config - 控制模式配置对象
         */
        function initControlModeConfigPage(config) {
            console.log('控制模式配置页面初始化...');

            // 初始化控制模式参数管理器
            controlModeManager = new ControlModeParameterManager(config);
            window.parameterManager = controlModeManager; // 设置为全局变量以兼容通用脚本

            // 初始化 MQTT 连接
            initMQTTConnection();

            // 定期更新连接状态显示和按钮状态
            setInterval(() => {
                if (mqttParameterManager) {
                    const status = mqttParameterManager.getConnectionStatus();
                    if (status.isConnected) {
                        updateMQTTStatus('connected', 'MQTT 已连接');
                    } else {
                        updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                    }
                    // 更新发送按钮状态
                    updateSendButtonStatus();
                }
            }, 1000);

            console.log('控制模式配置页面初始化完成');
        }

        /**
         * 发送参数设置到 MQTT 服务器（控制模式专用版本）
         */
        async function sendParameterSettings() {
            if (!controlModeManager) {
                showStatusMessage('参数管理器未初始化', 'error');
                return;
            }

            if (!mqttParameterManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            const connectionStatus = mqttParameterManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = controlModeManager.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }

            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = controlModeManager.getMQTTParameterArray();

                // 发送参数设置
                const result = await mqttParameterManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('参数设置发送成功:', result);

            } catch (error) {
                console.error('发送参数设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 显示状态消息（控制模式专用版本）
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型：success, error, warning
         */
        function showStatusMessage(message, type = 'success') {
            const statusElement = document.getElementById('status-message');
            if (!statusElement) return;

            statusElement.textContent = message;
            statusElement.className = `status-message ${type}`;
            statusElement.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 3000);
        }

        /**
         * 更新发送按钮状态（控制模式专用版本）
         */
        function updateSendButtonStatus() {
            const sendButton = document.getElementById('send-settings-btn');
            if (!sendButton) return;

            if (mqttParameterManager) {
                const status = mqttParameterManager.getConnectionStatus();
                if (status.isConnected) {
                    sendButton.disabled = false;
                    sendButton.title = '发送参数设置到 MQTT 服务器';
                } else {
                    sendButton.disabled = true;
                    sendButton.title = 'MQTT 未连接，无法发送参数设置';
                }
            } else {
                sendButton.disabled = true;
                sendButton.title = 'MQTT 管理器未初始化';
            }
        }

        /**
         * 更新 MQTT 状态显示（控制模式专用版本）
         * @param {string} status - 状态类型
         * @param {string} message - 状态消息
         */
        function updateMQTTStatus(status, message) {
            const statusElement = document.getElementById('mqtt-status');
            if (statusElement) {
                statusElement.className = `mqtt-connection-status ${status}`;
                statusElement.textContent = message;
            }
        }

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('控制模式配置页面初始化...');

            // 使用自定义的控制模式参数配置管理器初始化页面
            initControlModeConfigPage(controlModeConfig);

            // 设置定期更新连接状态和按钮状态
            setInterval(() => {
                if (mqttParameterManager) {
                    const status = mqttParameterManager.getConnectionStatus();
                    if (status.isConnected) {
                        updateMQTTStatus('connected', 'MQTT 已连接');
                    } else {
                        updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                    }
                    // 更新发送按钮状态
                    updateSendButtonStatus();
                }
            }, 1000);
        });
    </script>
</body>
</html>
