<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制模式测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #1a2332;
            color: white;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 20px;
        }
        .test-link {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
        }
        .info {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }
        .feature-list li:before {
            content: "✓ ";
            color: #00d4ff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #00d4ff;">控制模式配置页面测试</h1>
        
        <div class="info">
            <h3>页面功能特性：</h3>
            <ul class="feature-list">
                <li>三列布局设计，控制模式1占用更多空间</li>
                <li>支持开关控件（26个参数中的大部分）</li>
                <li>支持整数输入框（单元级数：10-12范围）</li>
                <li>支持下拉选择框（运行模式、直流电流抑制模式等）</li>
                <li>实时MQTT数据连接和同步</li>
                <li>参数修改高亮显示</li>
                <li>参数验证和错误提示</li>
                <li>工业监控界面深色科技主题</li>
            </ul>
        </div>

        <div class="info">
            <h3>参数分组：</h3>
            <p><strong>控制模式1组：</strong>26个参数，包含手动给定无功、控制模式选择、单元级数、相数、SVG连接方式等</p>
            <p><strong>控制模式2组：</strong>4个参数，包含无功补偿并联、电压补偿并联等</p>
            <p><strong>控制模式3组：</strong>2个参数，包含电压补偿点、负载无功快速检测</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="控制模式.html" class="test-link">打开控制模式配置页面</a>
            <a href="../debug1/保护使能.html" class="test-link">参考：保护使能页面</a>
        </div>

        <div class="info">
            <h3>技术实现说明：</h3>
            <p>1. 基于 debug1/保护使能.html 的结构和样式</p>
            <p>2. 扩展了通用参数配置管理器以支持多种控件类型</p>
            <p>3. 使用相同的MQTT连接和数据处理逻辑</p>
            <p>4. 保持了工业监控界面的一致性和响应式设计</p>
            <p>5. 页面尺寸：1920×1080像素，适配大屏显示</p>
        </div>
    </div>
</body>
</html>
