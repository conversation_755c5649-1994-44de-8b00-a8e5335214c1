<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制模式测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #1a2332;
            color: white;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 20px;
        }
        .test-link {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
        }
        .info {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }
        .feature-list li:before {
            content: "✓ ";
            color: #00d4ff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #00d4ff;">控制模式配置页面测试</h1>
        
        <div class="info">
            <h3>最新优化改进：</h3>
            <ul class="feature-list">
                <li>开关控件统一优化：解决了开关位置不一致和大小不统一的问题</li>
                <li>控件容器标准化：所有控件使用统一的容器布局</li>
                <li>状态文本固定宽度：防止文本长度影响开关位置</li>
                <li>表格行高优化：增加行高以容纳开关和状态文本</li>
                <li>滚动条优化：优化了布局高度计算，解决了1080p显示时的滚动条问题</li>
                <li>视觉协调统一：确保所有控件在垂直方向上完全对齐</li>
            </ul>
        </div>

        <div class="info">
            <h3>页面功能特性：</h3>
            <ul class="feature-list">
                <li>优化的布局设计：左侧大面板（控制模式1），右侧上下两个小面板（控制模式2和3）</li>
                <li>每行显示两个参数：左侧奇数序号，右侧偶数序号</li>
                <li>序号显示修复：正确显示连续数字序号（1, 2, 3, 4...）</li>
                <li>当前值显示优化：显示对应的文本而不是原始数字</li>
                <li>支持开关控件（26个参数中的大部分）</li>
                <li>支持整数输入框（单元级数：10-12范围）</li>
                <li>支持下拉选择框（运行模式、直流电流抑制模式等）</li>
                <li>实时MQTT数据连接和同步</li>
                <li>参数修改高亮显示</li>
                <li>参数验证和错误提示</li>
                <li>工业监控界面深色科技主题</li>
            </ul>
        </div>

        <div class="info">
            <h3>参数分组：</h3>
            <p><strong>控制模式1组：</strong>26个参数，包含手动给定无功、控制模式选择、单元级数、相数、SVG连接方式等</p>
            <p><strong>控制模式2组：</strong>4个参数，包含无功补偿并联、电压补偿并联等</p>
            <p><strong>控制模式3组：</strong>2个参数，包含电压补偿点、负载无功快速检测</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="控制模式.html" class="test-link">打开控制模式配置页面</a>
            <a href="../debug1/保护使能.html" class="test-link">参考：保护使能页面</a>
        </div>

        <div class="info">
            <h3>技术实现说明：</h3>
            <p>1. 基于 debug1/保护使能.html 的结构和样式</p>
            <p>2. 扩展了通用参数配置管理器以支持多种控件类型</p>
            <p>3. 重写了表格创建逻辑以支持每行两个参数的布局</p>
            <p>4. 优化了参数初始化和当前值显示逻辑</p>
            <p>5. 统一了所有控件的容器布局和样式</p>
            <p>6. 使用相同的MQTT连接和数据处理逻辑</p>
            <p>7. 保持了工业监控界面的一致性和响应式设计</p>
            <p>8. 页面尺寸：1920×1080像素，适配大屏显示</p>
        </div>
    </div>
</body>
</html>
