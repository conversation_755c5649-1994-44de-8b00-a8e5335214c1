# 控制模式配置页面

## 概述
基于 `debug1/保护使能.html` 文件创建的控制模式配置界面，保持相同的整体布局、样式和交互逻辑，但针对控制模式参数进行了专门的优化和扩展。

## 文件结构
- `控制模式.html` - 主要的控制模式配置页面
- `test.html` - 测试页面，用于验证功能
- `README.md` - 本说明文件

## 页面特性

### 布局设计
- **页面尺寸**: 1920×1080像素，适配大屏显示
- **三列布局**: 控制模式1、控制模式2、控制模式3
- **响应式设计**: 控制模式1占用更多空间（flex: 2），其他两列平分剩余空间（flex: 1）
- **深色科技主题**: 符合工业监控界面风格

### 参数配置

#### 控制模式1组（26个参数）
1. 手动给定无功 (1914_RPC) - 开关控件，自动/手动
2. 控制模式选择 (1914_CM) - 开关控件，间接/直接
3. 单元级数 (1914_UL) - 整数输入框，取值范围10~12
4. 相数 (1914_PN) - 开关控件，单相/三相
5. SVG连接方式 (1914_SCM) - 开关控件，星接/角接
6. 平衡控制使能 (1914_BCE) - 开关控件，无/有
7. 电压前馈模式 (1914_GVFF) - 开关控件，无滤波/加滤波
8. 空载模式使能 (1914_NLME) - 开关控件，不使能/使能
9. 锁相环模式 (1914_PM) - 开关控件，正常模式/SOGI模式
10. 低电压无功支撑 (1914_LVRSE) - 开关控件，不使能/使能
11. CT采样点位置 (1914_CSM) - 开关控件，电网侧/负载侧
12. 主从机模式 (1914_MSM) - 开关控件，从机/主机
13. 空载模式 (1914_NLM) - 开关控件，发感性/发容性
14. 母联状态 (1914_BTM) - 开关控件，不考虑/考虑
15. 无功补偿目标点 (1914_RCT) - 开关控件，低压侧/高压侧
16. 低电压穿越使能 (1914_LVRT) - 开关控件，正常补偿模式/低电压穿越模式
17. 快速无功补偿使能 (1914_FRCE) - 开关控件，不使能/使能
18. 测试模式 (1914_TM) - 开关控件，正常模式/方波测试模式
19. 柜体排列方式 (1914_CA) - 开关控件，右排列/左排列
20. 谐波补偿类型 (1914_HCAM) - 开关控件，自动运行/手动运行
21. 平衡控制电压指令 (1914_BCVC) - 开关控件，PWM板Ref/CPU板Ref
22. 运行模式 (1914_OM) - 下拉选择框，4个选项
23. AVC无功设定模式 (1914_ARSM) - 开关控件，不使能/使能
24. 电压补偿目标点 (1914_VCT) - 开关控件，低压侧/高压侧
25. 直流电流抑制模式 (1914_DCSM) - 下拉选择框，3个选项
26. 负序电流抑制模式 (1914_NSCS) - 开关控件，不使能/使能

#### 控制模式2组（4个参数）
1. 无功补偿并联 (1960_RPOE) - 开关控件，不使能/使能
2. 电压补偿并联 (1960_VPOE) - 开关控件，不使能/使能
3. 并联运行电压目标值同步 (1960_TVOVS) - 开关控件，不同步/同步
4. 备用 (1960_RSV) - 开关控件，备用参数

#### 控制模式3组（2个参数）
1. 电压补偿点 (1961_VCP) - 下拉选择框，4个选项
2. 负载无功快速检测 (1961_LRFDM) - 开关控件，PQ模式/负载电流检测

### 控件类型

#### 开关控件
- 统一的切换样式，旁边显示对应的状态文本
- 支持自定义状态文本（如：自动/手动、使能/不使能等）
- 点击切换，实时更新状态和高亮显示

#### 整数输入框
- 数值验证和范围限制
- 失焦时自动验证，超出范围时恢复原值并提示
- 支持键盘输入和鼠标操作

#### 下拉选择框
- 显示完整的选项文本
- 支持多个预定义选项
- 当前值显示对应的选项文本而不是数值

### 技术实现

#### 扩展的参数管理器
- 继承通用参数配置管理器 `ParameterConfigManager`
- 重写表格创建方法以支持三列布局
- 新增不同类型控件的创建和更新方法
- 保持与原有MQTT连接和数据处理逻辑的兼容性

#### MQTT集成
- 使用相同的MQTT服务器和主题配置
- 支持实时数据同步和参数下载
- 参数修改高亮显示，区分设定值和当前值

#### 界面优化
- 保持工业监控界面的一致性
- 响应式设计，适配大屏显示
- 科技感视觉效果和动画

## 使用方法

1. 打开 `控制模式.html` 页面
2. 等待MQTT连接建立（右上角显示连接状态）
3. 查看和修改各个参数的设定值
4. 修改的参数会高亮显示
5. 点击"下载"按钮将设定值发送到设备

## 兼容性
- 与现有的通用参数配置系统完全兼容
- 可以与其他参数配置页面共存
- 支持相同的MQTT协议和数据格式

## 维护说明
- 参数定义在JavaScript配置对象中，便于修改和扩展
- 样式使用CSS模块化设计，便于主题定制
- 代码结构清晰，便于功能扩展和维护
